
🔧 PRINT STATEMENT MIGRATION REPORT
=====================================

📊 SUMMARY:
Total print statements found: 422

📈 BY CATEGORY:
  ERROR: 9
  INFO: 392
  SUCCESS: 16
  WARNING: 5

🎯 BY PRIORITY (1=highest):
  Priority 1: 9
  Priority 2: 5
  Priority 3: 10
  Priority 4: 392
  Priority 5: 6

📁 TOP FILES WITH MOST PRINT STATEMENTS:
  src/cortex.py: 255
  src/ai_integration/lm_studio_client.py: 47
  src/file_generator.py: 44
  src/llm_rule_parser.py: 42
  src/behavioral_intelligence.py: 14
  src/pattern_walkforward_backtester.py: 10
  src/fact_checker.py: 7
  src/mt4_hardcoded_converter.py: 2
  src/flexible_pattern_parser.py: 1

🚨 HIGH PRIORITY STATEMENTS (Priority 1-2):
  src/cortex.py:2456 - ERROR -    ❌ NO PROFITABLE PATTERNS - Skipping file generation...
  src/cortex.py:2597 - ERROR - ❌ NOT: Chart patterns, technical indicators, or fundamental ...
  src/cortex.py:2612 - ERROR - \n❌ TERMINATING: No files will be processed without LLM avai...
  src/cortex.py:2692 - ERROR - ❌ NO PROFITABLE TRADING SYSTEMS FOUND...
  src/cortex.py:2704 - ERROR - 🔍 COMPLETE FAILURES:...
  src/file_generator.py:333 - WARNING -    ⚠️ No profitable patterns found in backtest results - gen...
  src/file_generator.py:336 - WARNING -    ⚠️ No validation results or backtest results - generating...
  src/file_generator.py:342 - WARNING -    ⚠️ No profitable patterns - generating empty EA...
  src/file_generator.py:427 - WARNING -    ⚠️ No MT4 EA code provided...
  src/file_generator.py:450 - WARNING -    ⚠️ No backtesting results - skipping HTML charts...
  src/ai_integration/lm_studio_client.py:263 - ERROR - ❌ No chat models available...
  src/ai_integration/lm_studio_client.py:314 - ERROR - \n❌ Operation cancelled...
  src/ai_integration/lm_studio_client.py:343 - ERROR - ❌ No models available...
  src/ai_integration/lm_studio_client.py:347 - ERROR - ❌ LM Studio server is not running...
