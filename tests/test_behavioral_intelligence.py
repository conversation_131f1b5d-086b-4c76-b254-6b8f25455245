import unittest
import os
import sys
import pandas as pd
import numpy as np
import importlib.util

REAL_DATA_PATH = os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_500_bars.csv')

# Ensure src is in sys.path before import
src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src'))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Import behavioral_intelligence module
import behavioral_intelligence

class TestBehavioralIntelligence(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        if not os.path.isfile(REAL_DATA_PATH):
            raise FileNotFoundError("UNBREAKABLE RULE VIOLATION: Real market data missing at {}".format(REAL_DATA_PATH))
        cls.data = pd.read_csv(REAL_DATA_PATH)
        # Enforce strict OHLCV capitalization
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_cols:
            if col not in cls.data.columns:
                raise ValueError(f"UNBREAKABLE RULE VIOLATION: Missing required column '{col}' in real data.")
        # Ensure DateTime index
        if 'DateTime' in cls.data.columns:
            cls.data['DateTime'] = pd.to_datetime(cls.data['DateTime'])
            cls.data.set_index('DateTime', inplace=True)

    def test_generate_orb_timeframes(self):
        tfs = behavioral_intelligence.generate_orb_timeframes(self.data)
        self.assertIsInstance(tfs, dict)
        # Should generate all expected ORB timeframes (intraday only)
        for tf in ['1min', '5min', '10min', '15min', '30min', '60min']:
            self.assertIn(tf, tfs)
            df = tfs[tf]
            # Enforce strict OHLCV capitalization
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                self.assertIn(col, df.columns)
            self.assertFalse(df.empty)

    def test_add_orb_context(self):
        # Use one timeframe for simplicity
        clean_5min = self.data.resample('5min').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum',
        }).dropna()
        enhanced = behavioral_intelligence.add_orb_context(clean_5min, '5min')
        self.assertIsInstance(enhanced, pd.DataFrame)
        # Check ORB-specific columns
        for col in ['bullish_bar', 'bearish_bar', 'bar_range', 'hour', 'timeframe', 'opening_range_high', 'opening_range_low']:
            self.assertIn(col, enhanced.columns)
        # Enforce OHLCV capitalization
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            self.assertIn(col, enhanced.columns)
        self.assertFalse(enhanced.empty)

    def test_generate_orb_summaries(self):
        tfs = behavioral_intelligence.generate_orb_timeframes(self.data)
        summary = behavioral_intelligence.generate_orb_summaries(tfs)
        self.assertIsInstance(summary, str)
        # ORB compliance: summary must mention all ORB timeframes
        for tf in ['1MIN', '5MIN', '10MIN', '15MIN', '30MIN', '60MIN']:
            self.assertIn(tf, summary)
        self.assertIn('Bullish', summary)
        self.assertIn('ORB Breakouts', summary)
        self.assertIn('Peak Hour', summary)
        self.assertTrue(len(summary) > 0)

    def test_missing_columns_error(self):
        # Test error handling for missing required columns
        bad_data = pd.DataFrame({'Price': [1, 2, 3]}, index=pd.date_range('2023-01-01', periods=3, freq='1min'))
        with self.assertRaises(ValueError) as context:
            behavioral_intelligence.generate_clean_timeframes(bad_data)
        self.assertIn('Data missing required columns', str(context.exception))

    def test_empty_data_handling(self):
        # Test handling of empty data after resampling
        empty_data = pd.DataFrame(columns=['Open', 'High', 'Low', 'Close', 'Volume'])
        empty_data.index = pd.DatetimeIndex([])
        result = behavioral_intelligence.generate_clean_timeframes(empty_data)
        self.assertIsInstance(result, dict)
        # Should handle empty data gracefully

    def test_missing_volume_column(self):
        # Test handling when Volume column is missing
        data_no_vol = self.data.drop('Volume', axis=1)
        result = behavioral_intelligence.generate_clean_timeframes(data_no_vol)
        self.assertIsInstance(result, dict)
        # Should add Volume column with NaN values

    def test_simplified_behavioral_intelligence(self):
        # Test simplified mode by temporarily modifying config
        import config
        original_simplified = getattr(config.config, 'SIMPLIFIED_BEHAVIORAL_INTELLIGENCE', False)
        try:
            config.config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = True
            clean_5min = self.data.resample('5min').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum',
            }).dropna()
            enhanced = behavioral_intelligence.add_behavioral_intelligence(clean_5min, '5min')
            # Should have minimal columns in simplified mode
            self.assertIn('hour', enhanced.columns)
            self.assertIn('timeframe', enhanced.columns)
            self.assertIn('bullish', enhanced.columns)
            self.assertIn('bearish', enhanced.columns)
            self.assertEqual(enhanced['timeframe'].iloc[0], '5min')
        finally:
            config.config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = original_simplified

    def test_add_behavioral_intelligence_error_handling(self):
        # Test error handling in add_behavioral_intelligence with bad data
        bad_data = pd.DataFrame({
            'Open': [1, 2, 3],
            'High': [1.1, 2.1, 3.1],
            'Low': [0.9, 1.9, 2.9],
            'Close': [1.05, 2.05, 3.05],
            'Volume': [100, 200, 300]
        })
        # Create non-datetime index to trigger error path
        bad_data.index = ['a', 'b', 'c']
        result = behavioral_intelligence.add_behavioral_intelligence(bad_data, '5min')
        # Should still return a DataFrame with basic columns
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('hour', result.columns)
        self.assertIn('timeframe', result.columns)

    def test_get_bars_per_day_helper(self):
        # Test the _get_bars_per_day helper function
        self.assertEqual(behavioral_intelligence._get_bars_per_day('5min'), 288)
        self.assertEqual(behavioral_intelligence._get_bars_per_day('15min'), 96)
        self.assertEqual(behavioral_intelligence._get_bars_per_day('30min'), 48)
        self.assertEqual(behavioral_intelligence._get_bars_per_day('1h'), 24)
        self.assertEqual(behavioral_intelligence._get_bars_per_day('4h'), 6)
        self.assertEqual(behavioral_intelligence._get_bars_per_day('1d'), 1)
        self.assertEqual(behavioral_intelligence._get_bars_per_day('1w'), 1/7)
        # Test unknown timeframe
        self.assertEqual(behavioral_intelligence._get_bars_per_day('unknown'), 24)

    def test_generate_behavioral_summaries_edge_cases(self):
        # Test with empty dataframes
        empty_tfs = {'5min': pd.DataFrame()}
        summary = behavioral_intelligence.generate_behavioral_summaries(empty_tfs)
        self.assertIsInstance(summary, str)
        
        # Test with minimal data
        minimal_data = pd.DataFrame({
            'Open': [1.0],
            'High': [1.1],
            'Low': [0.9],
            'Close': [1.05],
            'Volume': [100],
            'bullish': [True],
            'bearish': [False],
            'range_breakout': [False],
            'trending': [True],
            'volatility': [0.05],
            'high_volume': [False],
            'hour': [10]
        }, index=pd.date_range('2023-01-01', periods=1, freq='5min'))
        
        minimal_tfs = {'5min': minimal_data}
        summary = behavioral_intelligence.generate_behavioral_summaries(minimal_tfs)
        self.assertIn('5MIN', summary)
        self.assertIn('Bullish', summary)
        self.assertIn('Peak Hour: 10', summary)

    def test_full_behavioral_intelligence_features(self):
        # Test that full mode generates all expected features
        import config
        original_simplified = getattr(config.config, 'SIMPLIFIED_BEHAVIORAL_INTELLIGENCE', False)
        try:
            config.config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = False
            clean_5min = self.data.resample('5min').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum',
            }).dropna()
            enhanced = behavioral_intelligence.add_behavioral_intelligence(clean_5min, '5min')
            
            # Check for advanced features
            expected_features = [
                'body_size', 'wick_upper', 'wick_lower', 'range_size',
                'price_change', 'momentum_3', 'momentum_5',
                'volatility', 'volatility_ma', 'high_volatility',
                'resistance_level', 'support_level', 'near_resistance', 'near_support',
                'breakout_up', 'breakout_down', 'range_breakout',
                'day_of_week', 'is_session_start', 'is_overlap',
                'trend_strength', 'trending', 'ranging',
                'hammer', 'shooting_star', 'engulfing_bull', 'engulfing_bear',
                'bars_per_day', 'intraday', 'daily_plus'
            ]
            
            for feature in expected_features:
                self.assertIn(feature, enhanced.columns, f"Missing feature: {feature}")
                
        finally:
            config.config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = original_simplified

    def test_full_behavioral_intelligence_features(self):
        # Test all the behavioral intelligence features in full mode
        import config
        original_value = config.config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE
        try:
            config.config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = False
            clean_5min = self.data.resample('5min').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum',
            }).dropna()
            enhanced = behavioral_intelligence.add_behavioral_intelligence(clean_5min, '5min')
            
            # Test all expected columns are present
            expected_cols = [
                'body_size', 'wick_upper', 'wick_lower', 'range_size',
                'bullish', 'bearish', 'doji', 'price_change', 'momentum_3', 'momentum_5',
                'volatility', 'volatility_ma', 'high_volatility',
                'resistance_level', 'support_level', 'near_resistance', 'near_support',
                'breakout_up', 'breakout_down', 'range_breakout',
                'hour', 'day_of_week', 'is_session_start', 'is_overlap',
                'trend_strength', 'trending', 'ranging',
                'high_volume', 'volume_breakout',
                'hammer', 'shooting_star', 'engulfing_bull', 'engulfing_bear',
                'timeframe', 'bars_per_day', 'intraday', 'daily_plus'
            ]
            
            for col in expected_cols:
                self.assertIn(col, enhanced.columns, f"Missing column: {col}")
                
        finally:
            config.config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = original_value

    def test_volume_handling_with_nan(self):
        # Test handling when volume is all NaN
        data_nan_vol = self.data.copy()
        data_nan_vol['Volume'] = np.nan
        clean_5min = data_nan_vol.resample('5min').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum',
        }).dropna()
        enhanced = behavioral_intelligence.add_behavioral_intelligence(clean_5min, '5min')
        self.assertIn('high_volume', enhanced.columns)
        self.assertIn('volume_breakout', enhanced.columns)
    
    def test_day_of_week_string_mapping(self):
        # Test day_of_week string to integer mapping exception handling
        test_data = self.data.head(10).copy()
        test_data['day_of_week'] = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
        
        enhanced = behavioral_intelligence.add_behavioral_intelligence(test_data, '1min')
        self.assertIn('day_of_week_num', enhanced.columns)
        self.assertEqual(enhanced['day_of_week_num'].iloc[0], 0)  # Monday = 0
        self.assertEqual(enhanced['day_of_week_num'].iloc[1], 1)  # Tuesday = 1
    
    def test_day_of_week_invalid_string_mapping(self):
        # Test day_of_week with invalid strings that should default to 0
        test_data = self.data.head(5).copy()
        test_data['day_of_week'] = ['InvalidDay', 'AnotherInvalid', 'Monday', 'BadDay', 'Tuesday']
        
        enhanced = behavioral_intelligence.add_behavioral_intelligence(test_data, '1min')
        self.assertIn('day_of_week_num', enhanced.columns)
        self.assertEqual(enhanced['day_of_week_num'].iloc[0], 0)  # Invalid should map to 0
        self.assertEqual(enhanced['day_of_week_num'].iloc[2], 0)  # Monday = 0

    def test_pattern_recognition(self):
        # Test pattern recognition features
        clean_5min = self.data.resample('5min').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum',
        }).dropna()
        enhanced = behavioral_intelligence.add_behavioral_intelligence(clean_5min, '5min')
        
        # Test pattern columns exist
        pattern_cols = ['hammer', 'shooting_star', 'engulfing_bull', 'engulfing_bear']
        for col in pattern_cols:
            self.assertIn(col, enhanced.columns)
            self.assertTrue(enhanced[col].dtype == bool)

    def test_timeframe_classification(self):
        # Test timeframe classification
        test_cases = [
            ('5min', True, False),
            ('15min', True, False),
            ('1h', True, False),
            ('1d', False, True),
            ('1w', False, True)
        ]
        
        for tf, expected_intraday, expected_daily_plus in test_cases:
            clean_data = self.data.resample('5min').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum',
            }).dropna()
            enhanced = behavioral_intelligence.add_behavioral_intelligence(clean_data, tf)
            self.assertEqual(enhanced['intraday'].iloc[0], expected_intraday)
            self.assertEqual(enhanced['daily_plus'].iloc[0], expected_daily_plus)

    def test_error_handling_invalid_datetime_index(self):
        # Test error handling with invalid datetime index
        bad_data = pd.DataFrame({
            'Open': [1, 2, 3],
            'High': [1.1, 2.1, 3.1],
            'Low': [0.9, 1.9, 2.9],
            'Close': [1.05, 2.05, 3.05],
            'Volume': [100, 200, 300]
        }, index=['invalid1', 'invalid2', 'invalid3'])
        
        result = behavioral_intelligence.add_behavioral_intelligence(bad_data, '5min')
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('hour', result.columns)
        self.assertIn('timeframe', result.columns)

    def test_all_timeframe_bars_per_day(self):
        # Test all timeframe mappings in _get_bars_per_day
        test_cases = [
            ('5min', 288),
            ('15min', 96),
            ('30min', 48),
            ('1h', 24),
            ('4h', 6),
            ('1d', 1),
            ('1w', 1/7),
            ('unknown_tf', 24)  # Default case
        ]
        
        for tf, expected in test_cases:
            result = behavioral_intelligence._get_bars_per_day(tf)
            self.assertEqual(result, expected)

    def test_behavioral_summaries_comprehensive(self):
        # Test comprehensive behavioral summaries with real data
        tfs = behavioral_intelligence.generate_clean_timeframes(self.data)
        summary = behavioral_intelligence.generate_behavioral_summaries(tfs)
        
        # Check that summary contains expected elements
        self.assertIsInstance(summary, str)
        self.assertTrue(len(summary) > 0)
        
        # Should contain timeframe names
        for tf in ['5MIN', '15MIN', '1H', '1D']:
            self.assertIn(tf, summary)
            
        # Should contain key metrics
        metrics = ['Bullish', 'Breakouts', 'Trending', 'Volatility', 'Recent', 'Peak Hour', 'Participation']
        for metric in metrics:
            self.assertIn(metric, summary)

    def test_generate_clean_timeframes_edge_cases(self):
        # Test edge cases in timeframe generation
        
        # Test with minimal data
        minimal_data = self.data.head(10)
        result = behavioral_intelligence.generate_clean_timeframes(minimal_data)
        self.assertIsInstance(result, dict)
        
        # Test with data that results in empty resampling
        single_row = self.data.head(1)
        result = behavioral_intelligence.generate_clean_timeframes(single_row)
        self.assertIsInstance(result, dict)

    def test_infinite_and_nan_cleanup(self):
        # Test that infinite and NaN values are properly cleaned up
        clean_5min = self.data.resample('5min').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum',
        }).dropna()
        
        enhanced = behavioral_intelligence.add_behavioral_intelligence(clean_5min, '5min')
        
        # Check that no infinite values remain
        for col in enhanced.select_dtypes(include=[np.number]).columns:
            self.assertFalse(np.isinf(enhanced[col]).any(), f"Infinite values found in {col}")

    def test_timeframe_generation_exception_handling(self):
        # Test exception handling in timeframe generation
        # Create data that will cause resampling issues
        bad_data = pd.DataFrame({
            'Open': [1, 2, 3],
            'High': [1.1, 2.1, 3.1],
            'Low': [0.9, 1.9, 2.9],
            'Close': [1.05, 2.05, 3.05],
            'Volume': [100, 200, 300]
        }, index=pd.date_range('2023-01-01', periods=3, freq='1s'))  # Very short timeframe
        
        # Mock the resample to raise an exception
        import unittest.mock
        with unittest.mock.patch.object(bad_data, 'resample') as mock_resample:
            mock_resample.side_effect = Exception("Resampling failed")
            result = behavioral_intelligence.generate_clean_timeframes(bad_data)
            self.assertIsInstance(result, dict)

    def test_volume_missing_handling(self):
        # Test handling when volume data is completely missing (not just NaN)
        data_no_volume = self.data.drop('Volume', axis=1)
        clean_5min = data_no_volume.resample('5min').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
        }).dropna()
        
        enhanced = behavioral_intelligence.add_behavioral_intelligence(clean_5min, '5min')
        
        # Should use price-based participation proxies
        self.assertIn('high_volume', enhanced.columns)
        self.assertIn('volume_breakout', enhanced.columns)
        # high_volume should equal high_volatility when volume is missing
        self.assertTrue((enhanced['high_volume'] == enhanced['high_volatility']).all())

    def test_day_of_week_string_mapping(self):
        # Test day of week string to number mapping
        # Create valid datetime index data
        dates = pd.date_range('2023-01-01', periods=3, freq='5min')
        test_data = pd.DataFrame({
            'Open': [1, 2, 3],
            'High': [1.1, 2.1, 3.1],
            'Low': [0.9, 1.9, 2.9],
            'Close': [1.05, 2.05, 3.05],
            'Volume': [100, 200, 300],
            'day_of_week': ['Monday', 'Tuesday', 'Wednesday']  # String values to trigger mapping
        }, index=dates)
        
        result = behavioral_intelligence.add_behavioral_intelligence(test_data, '5min')
        
        # Should have mapped string days to numbers
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('day_of_week_num', result.columns)
        # Monday should map to 0, Tuesday to 1, Wednesday to 2
        self.assertEqual(result['day_of_week_num'].iloc[0], 0)
        self.assertEqual(result['day_of_week_num'].iloc[1], 1)
        self.assertEqual(result['day_of_week_num'].iloc[2], 2)

    def test_behavioral_intelligence_exception_recovery(self):
        # Test that behavioral intelligence handles exceptions and returns basic data
        # Create data that will cause issues in behavioral intelligence calculation
        bad_data = pd.DataFrame({
            'Open': [np.inf, -np.inf, np.nan],
            'High': [np.inf, -np.inf, np.nan],
            'Low': [np.inf, -np.inf, np.nan],
            'Close': [np.inf, -np.inf, np.nan],
            'Volume': [np.inf, -np.inf, np.nan]
        }, index=['invalid1', 'invalid2', 'invalid3'])
        
        result = behavioral_intelligence.add_behavioral_intelligence(bad_data, '5min')
        
        # Should return a DataFrame with basic required columns
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('hour', result.columns)
        self.assertIn('timeframe', result.columns)
        
        # Should have all required enhancement columns
        required_columns = [
            'volatility_regime', 'trend_regime', 'momentum_1', 'momentum_continues',
            'session', 'session_transition', 'potential_breakout_up', 'range_size', 'near_significant_level'
        ]
        for col in required_columns:
            self.assertIn(col, result.columns)
    
    def test_config_simplified_mode_true(self):
        # Test simplified mode when config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE is True
        import config
        original_value = getattr(config.config, 'SIMPLIFIED_BEHAVIORAL_INTELLIGENCE', False)
        try:
            config.config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = True
            clean_5min = self.data.resample('5min').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum',
            }).dropna()
            
            result = behavioral_intelligence.add_behavioral_intelligence(clean_5min, '5min')
            self.assertIsInstance(result, pd.DataFrame)
            self.assertIn('hour', result.columns)
            self.assertIn('timeframe', result.columns)
            self.assertIn('bullish', result.columns)
            self.assertIn('bearish', result.columns)
            # In simplified mode, should have fewer columns
            self.assertNotIn('body_size', result.columns)
        finally:
            config.config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = original_value
    
    def test_datetime_index_conversion_failure(self):
        # Test when datetime index conversion fails
        bad_data = pd.DataFrame({
            'Open': [1, 2, 3],
            'High': [1.1, 2.1, 3.1],
            'Low': [0.9, 1.9, 2.9],
            'Close': [1.05, 2.05, 3.05],
            'Volume': [100, 200, 300]
        }, index=[object(), object(), object()])  # Non-convertible objects
        
        result = behavioral_intelligence.add_behavioral_intelligence(bad_data, '5min')
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('hour', result.columns)
        self.assertEqual(result['hour'].iloc[0], 0)  # Should default to 0
    
    def test_missing_day_of_week_column(self):
        # Test when day_of_week column is missing entirely
        clean_5min = self.data.resample('5min').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum',
        }).dropna()
        
        # Ensure day_of_week is not in columns
        if 'day_of_week' in clean_5min.columns:
            clean_5min = clean_5min.drop('day_of_week', axis=1)
            
        result = behavioral_intelligence.add_behavioral_intelligence(clean_5min, '5min')
        self.assertIn('day_of_week_num', result.columns)
        self.assertEqual(result['day_of_week_num'].iloc[0], 0)  # Should default to 0

if __name__ == '__main__':
    unittest.main()
