#!/usr/bin/env python3
"""
Backtesting Walk-Forward Validator

Integrates walk-forward analysis with backtesting-only approach.
Only profitable patterns advance to MT4 generation.
"""

import pandas as pd
from typing import Dict, Any, Optional
import logging
from backtesting import Strategy
from walkforward_tester import WalkForwardTester
from backtesting_rule_parser import BacktestingTradingRule, BacktestingRuleParser
from config import config

logger = logging.getLogger(__name__)

class BacktestingWalkForwardValidator:
    """Validates backtesting patterns using walk-forward analysis"""
    
    def __init__(self, min_return_threshold: float = None,
                 min_consistency_score: float = None,
                 min_win_rate: float = None,
                 max_drawdown_threshold: float = None):
        """
        Initialize validator with profitability thresholds

        Args:
            min_return_threshold: Minimum average return % required (uses config if None)
            min_consistency_score: Minimum consistency score required (uses config if None)
            min_win_rate: Minimum win rate % required (uses config if None)
            max_drawdown_threshold: Maximum drawdown % allowed (uses config if None)
        """
        self.min_return_threshold = min_return_threshold or config.VALIDATION_MIN_RETURN
        self.min_consistency_score = min_consistency_score or config.VALIDATION_MIN_CONSISTENCY
        self.min_win_rate = min_win_rate or config.VALIDATION_MIN_WIN_RATE
        self.max_drawdown_threshold = max_drawdown_threshold or config.VALIDATION_MAX_DRAWDOWN
        
        self.walk_forward_tester = WalkForwardTester(n_splits=2)  # 2 folds for faster validation
        
    def validate_patterns(self, llm_response: str, ohlc_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate LLM patterns using walk-forward analysis
        
        Args:
            llm_response: LLM response with backtesting patterns
            ohlc_data: OHLC data for validation
            
        Returns:
            Dict with validation results and profitable patterns
        """
        print("🔄 Starting Walk-Forward Validation...")
        
        try:
            # Parse patterns using backtesting-only parser
            parser = BacktestingRuleParser()
            rules = parser.parse_llm_response(llm_response)
            
            if not rules:
                return {
                    'success': False,
                    'error': 'No patterns parsed from LLM response',
                    'profitable_patterns': [],
                    'validation_results': {}
                }
            
            print(f"📊 Validating {len(rules)} patterns...")
            
            # Validate each pattern individually
            validation_results = {}
            profitable_patterns = []
            
            for rule in rules:
                print(f"   🧪 Testing Pattern {rule.rule_id}: {rule.name}")
                
                # Create strategy for this specific pattern
                pattern_strategy = self._create_pattern_strategy(rule)
                
                # Run walk-forward validation
                wf_results = self._run_walk_forward_validation(
                    pattern_strategy, ohlc_data, rule
                )
                
                # Evaluate profitability
                is_profitable = self._evaluate_profitability(wf_results)
                
                validation_results[rule.rule_id] = {
                    'rule': rule,
                    'walk_forward_results': wf_results,
                    'is_profitable': is_profitable,
                    'metrics': self._extract_key_metrics(wf_results) if wf_results else {}
                }
                
                if is_profitable:
                    profitable_patterns.append(rule)
                    print(f"      ✅ Pattern {rule.rule_id} PASSED validation")
                else:
                    print(f"      ❌ Pattern {rule.rule_id} FAILED validation")
            
            success_rate = len(profitable_patterns) / len(rules) * 100
            print(f"📊 Validation Complete: {len(profitable_patterns)}/{len(rules)} patterns profitable ({success_rate:.1f}%)")
            
            return {
                'success': True,
                'total_patterns': len(rules),
                'profitable_patterns': profitable_patterns,
                'validation_results': validation_results,
                'success_rate': success_rate
            }
            
        except Exception as e:
            logger.error(f"Walk-forward validation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'profitable_patterns': [],
                'validation_results': {}
            }
    
    def _create_pattern_strategy(self, rule: BacktestingTradingRule):
        """
        UNBREAKABLE RULE COMPLIANCE: Use the exact same strategy creation logic as main backtesting
        This ensures 100% consistency and prevents manual coding of backtesting logic
        """

        # CRITICAL: Use the exact same strategy creation logic as main backtesting
        # This ensures we use the SAME backtesting implementation, not a manual copy

        # Get the rule function using the same parser as main backtesting
        from backtesting_rule_parser import parse_backtesting_rules

        # Convert the rule back to text format for parsing (maintains consistency)
        rule_text = f"""
        Pattern Name: {rule.pattern_name}
        Entry Conditions: {rule.entry_conditions}
        Exit Conditions: {rule.exit_conditions}
        Position Sizing: {rule.position_sizing}
        """

        # Parse using the same function as main backtesting
        rule_functions = parse_backtesting_rules(rule_text)

        if not rule_functions:
            raise ValueError(f"Failed to parse rule for walk-forward validation: {rule.pattern_name}")

        rule_func = rule_functions[0]  # Get the first (and only) rule function
        pattern_text = rule_text

        # CRITICAL: Use the EXACT SAME PatternStrategy class creation logic as cortex.py
        # This is copied directly from cortex._orchestrate_backtesting to ensure 100% consistency
        class PatternStrategy(Strategy):
            def init(self):
                try:
                    self.rule_functions = [rule_func]
                    # Store full ORB-enhanced dataset for rule evaluation
                    # Note: orb_enhanced_data will be set by the caller
                    self.full_ohlc_data = None  # Will be set by walk-forward tester
                    self.pattern_text = pattern_text
                    # Initialize counters for diagnostics
                    self.signal_count = 0
                    self._order_rejection_count = 0
                    self.bars_processed = 0
                except Exception as e:
                    print(f"      ❌ CRITICAL ERROR in PatternStrategy.init(): {e}")
                    raise

            def next(self):
                try:
                    # Use the EXACT SAME next() logic as main backtesting
                    if not hasattr(self, '_current_bar_index'):
                        self._current_bar_index = 1
                    else:
                        self._current_bar_index += 1

                    current_idx = self._current_bar_index
                    self.bars_processed += 1

                    # Use the same signal generation logic as main backtesting
                    if self.full_ohlc_data is not None:
                        signal = rule_func(self.full_ohlc_data, current_idx)
                    else:
                        # Fallback to using self.data if full_ohlc_data not available
                        current_data = pd.DataFrame({
                            'Open': self.data.Open[:current_idx + 1],
                            'High': self.data.High[:current_idx + 1],
                            'Low': self.data.Low[:current_idx + 1],
                            'Close': self.data.Close[:current_idx + 1],
                            'Volume': getattr(self.data, 'Volume', [1000] * (current_idx + 1))[:current_idx + 1]
                        })
                        signal = rule_func(current_data, current_idx)

                    # Use the EXACT SAME signal processing logic as main backtesting
                    if signal:
                        self.signal_count += 1

                        direction = signal.get('direction', 'long')
                        entry_price = signal.get('entry_price')
                        stop_loss = signal.get('stop_loss')
                        take_profit = signal.get('take_profit')

                        # Use fractional equity sizing like main backtesting
                        position_size = config.DEFAULT_POSITION_SIZE_PCT / 100

                        try:
                            if direction == 'long':
                                order = self.buy(size=position_size, limit=entry_price, sl=stop_loss, tp=take_profit)
                            else:
                                order = self.sell(size=position_size, limit=entry_price, sl=stop_loss, tp=take_profit)

                            if not order:
                                self._order_rejection_count += 1

                        except Exception as order_error:
                            self._order_rejection_count += 1

                except Exception as e:
                    print(f"      ❌ CRITICAL ERROR in PatternStrategy.next(): {e}")
                    raise

        return PatternStrategy
    
    def _run_walk_forward_validation(self, strategy_class, ohlc_data: pd.DataFrame,
                                   rule: BacktestingTradingRule) -> Optional[Dict[str, Any]]:
        """Run walk-forward validation for a single pattern"""
        try:
            # Ensure minimum data size for walk-forward
            if len(ohlc_data) < 100:
                print(f"      ⚠️ Insufficient data for walk-forward validation ({len(ohlc_data)} bars)")
                return None
            
            # Run walk-forward test
            wf_results = self.walk_forward_tester.run_walk_forward_test(
                data=ohlc_data,
                strategy_class=strategy_class,
                strategy_params={},
                backtest_params={
                    'cash': config.VALIDATION_INITIAL_CASH,
                    'commission': config.VALIDATION_COMMISSION,
                    'spread': config.VALIDATION_SPREAD,
                    'margin': config.VALIDATION_MARGIN,
                    'exclusive_orders': True
                }
            )
            
            return wf_results
            
        except Exception as e:
            print(f"      ❌ Walk-forward validation failed: {e}")
            return None
    
    def _evaluate_profitability(self, wf_results: Optional[Dict[str, Any]]) -> bool:
        """Evaluate if pattern meets profitability criteria"""
        if not wf_results or not wf_results.get('summary'):
            return False
        
        summary = wf_results['summary']
        
        # Check key metrics
        avg_return = summary.get('avg_return', 0)
        consistency_score = summary.get('consistency_score', 0)
        avg_win_rate = summary.get('avg_win_rate', 0)
        max_drawdown = summary.get('max_drawdown', 100)
        
        # Apply thresholds
        meets_return = avg_return >= self.min_return_threshold
        meets_consistency = consistency_score >= self.min_consistency_score
        meets_win_rate = avg_win_rate >= self.min_win_rate
        meets_drawdown = max_drawdown <= self.max_drawdown_threshold
        
        return meets_return and meets_consistency and meets_win_rate and meets_drawdown
    
    def _extract_key_metrics(self, wf_results: Dict[str, Any]) -> Dict[str, float]:
        """Extract key metrics from walk-forward results"""
        if not wf_results or not wf_results.get('summary'):
            return {}
        
        summary = wf_results['summary']
        
        return {
            'avg_return': summary.get('avg_return', 0),
            'consistency_score': summary.get('consistency_score', 0),
            'avg_win_rate': summary.get('avg_win_rate', 0),
            'max_drawdown': summary.get('max_drawdown', 0),
            'total_trades': summary.get('total_trades', 0),
            'avg_sharpe': summary.get('avg_sharpe', 0)
        }
    
    def generate_validation_report(self, validation_results: Dict[str, Any]) -> str:
        """Generate a validation report"""
        if not validation_results.get('success'):
            return f"❌ Validation Failed: {validation_results.get('error', 'Unknown error')}"
        
        total_patterns = validation_results['total_patterns']
        profitable_patterns = validation_results['profitable_patterns']
        success_rate = validation_results['success_rate']
        
        report = f"""# 🔄 Walk-Forward Validation Report

## 📊 Summary
- **Total Patterns Tested**: {total_patterns}
- **Profitable Patterns**: {len(profitable_patterns)}
- **Success Rate**: {success_rate:.1f}%

## 🎯 Profitable Patterns Ready for MT4 Generation:
"""
        
        for pattern in profitable_patterns:
            pattern_results = validation_results['validation_results'][pattern.rule_id]
            metrics = pattern_results['metrics']
            
            report += f"""
### Pattern {pattern.rule_id}: {pattern.name}
- **Average Return**: {metrics.get('avg_return', 0):.2f}%
- **Win Rate**: {metrics.get('avg_win_rate', 0):.1f}%
- **Max Drawdown**: {metrics.get('max_drawdown', 0):.2f}%
- **Total Trades**: {metrics.get('total_trades', 0)}
- **Entry Logic**: {pattern.entry_logic}
- **Direction**: {pattern.direction}
"""
        
        if not profitable_patterns:
            report += "\n❌ No patterns met profitability criteria for MT4 generation."
        
        return report

# Main function for integration
def validate_backtesting_patterns(llm_response: str, ohlc_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Main function to validate backtesting patterns with walk-forward analysis
    
    Args:
        llm_response: LLM response with backtesting patterns
        ohlc_data: OHLC data for validation
        
    Returns:
        Dict with validation results and profitable patterns
    """
    validator = BacktestingWalkForwardValidator()
    return validator.validate_patterns(llm_response, ohlc_data)
